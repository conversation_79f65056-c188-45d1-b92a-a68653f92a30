import {getWorkspaceFileResult} from '@api/WorkArea';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import FilePathSelectWorkarea, {FileNodeTypeEnum} from '@components/FilePathSelectWorkarea';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import JobGlobalParamsFormItem from '@pages/JobWorkflow/Jobs/components/FormItem/JobGlobalParamsFormItem';
import JobListParamsFormItem from '@pages/JobWorkflow/Jobs/components/FormItem/JobListParamsFormItem';
import {IAppState} from '@store/index';
import {Button, Col, Form, Input, Row, Table} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React, {useContext, useMemo} from 'react';
import {useSelector} from 'react-redux';

const SparkJarTaskParams: React.FC<{form?: FormInstance}> = ({form}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {workspaceId} = useContext(WorkspaceContext);
  const envVars = Form.useWatch('envVars', form);

  const columns = [
    {
      title: '参数名',
      dataIndex: 'key',
      key: 'key',
      width: 70,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    },
    {
      title: '参数值',
      dataIndex: 'value',
      key: 'value',
      width: 100,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    }
  ];

  return (
    <>
      {isEditing ? (
        <>
          <Form.Item
            label="依赖库"
            name="dependentLibraries"
            style={{marginBottom: 0}}
            rules={isEditing ? [{required: true, message: '请输入依赖库'}] : []}
          ></Form.Item>
          <Form.List name="dependentLibraries">
            {(fieldsTem, {add, remove}) => {
              // 默认空的时候 也需要展示一条
              const fields = fieldsTem.length === 0 ? [{key: Date.now(), name: 0}] : fieldsTem;
              return (
                <>
                  {fields.map((field, index) => (
                    <div key={field.key}>
                      <Row key={field.key} gutter={8}>
                        <Col flex={'1 1 100px'}>
                          <Form.Item
                            key={field.key}
                            {...field}
                            name={[field.name]}
                            dependencies={[...fields.map(({name}) => ['dependentLibraries', name])]}
                            rules={
                              isEditing
                                ? [
                                    {required: true, message: '请输入运行代码路径'},
                                    ({getFieldsValue}) => ({
                                      validator: (rule, value, callback) => {
                                        if (!value) {
                                          callback();
                                          return;
                                        }
                                        // 检查重复性
                                        const allFields = getFieldsValue()['dependentLibraries'] || [];
                                        const duplicateCount = allFields.filter(
                                          (field: string) => field === value
                                        ).length;

                                        if (duplicateCount > 1) {
                                          return Promise.reject(new Error('参数名不能重复'));
                                        }
                                        // 检查路径有效性
                                        getWorkspaceFileResult({path: value, workspaceId}).then((res) => {
                                          if (res.result?.message) {
                                            callback(res.result.message);
                                          } else if (res.result?.nodeType !== FileNodeTypeEnum.FILE) {
                                            callback('输入路径为不是jar，请选择jar');
                                          } else {
                                            callback();
                                          }
                                        });
                                      }
                                    })
                                  ]
                                : []
                            }
                          >
                            <EditableContent isEditing={isEditing}>
                              <FilePathSelectWorkarea
                                selectNodeType={FileNodeTypeEnum.FILE}
                                selectFileSuffix={['jar']}
                              />
                            </EditableContent>
                          </Form.Item>
                        </Col>
                        <Col flex={'0 0 16px'}>
                          <Button
                            type="text"
                            onClick={() => remove(field.name)}
                            icon={<IconSvg type="delete" />}
                            disabled={fields.length === 1}
                          ></Button>
                        </Col>
                      </Row>
                      {index === fields.length - 1 && (
                        <div>
                          <Button
                            style={{marginBottom: '16px', marginLeft: '-11px'}}
                            type="actiontext"
                            icon={<IconSvg type="add" />}
                            onClick={() => add('')}
                          >
                            添加参数
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </>
              );
            }}
          </Form.List>
        </>
      ) : (
        <JobListParamsFormItem
          label="依赖库"
          formName="dependentLibraries"
          isEditing={isEditing}
          form={form}
        />
      )}

      <Form.Item
        label="主类名称"
        name="mainClass"
        rules={isEditing ? [{required: true, message: '请输入主类名称'}] : []}
      >
        <EditableContent isEditing={isEditing}>
          <Input placeholder="com.example.Main" />
        </EditableContent>
      </Form.Item>

      <JobListParamsFormItem label="主类参数" formName="mainClassArgs" isEditing={isEditing} form={form} />

      <Form.Item label="环境变量" name="envVars" style={{marginBottom: 0}} />
      {isEditing ? (
        <JobGlobalParamsFormItem keyWidth="1 0 150px" formName="envVars" />
      ) : (
        <Table dataSource={envVars} columns={columns} pagination={false} />
      )}
    </>
  );
};

export default SparkJarTaskParams;
