import {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {debounce} from 'lodash';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';

import {CatalogType, EnumMetaType} from '@api/metaRequest';
import DatasetAndModelFileInfoTable, {
  DatasetAndModelFileRefHandle
} from '../components/DatasetAndModelFileInfoTable';
import CreateDatasetAndModelVersionModal from '../components/CreateDatasetAndModelVersionModal';
import {RULE} from '@utils/regs';
import {OutlinedPlusNew} from 'acud-icon';

import {Privilege, ResourceType} from '@api/permission/type';
import {WorkspaceContext} from '@pages/index';
import PermissionManage from '@components/PermissionManage';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2',
  PERMISSION = '3'
}

const PanelModel = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, userList, canWrite} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW} = urlState;

  const dropdownMenu = useMemo(
    () => [
      {key: 'rename', label: '重命名模型', disable: !canWrite, authName: Privilege.Manage},
      {key: 'remove', label: '删除模型', disable: !canWrite, authName: Privilege.Manage}
    ],
    [canWrite]
  );

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 文件信息 Table 组件的 Ref
  const fileInfoTableRef = useRef<DatasetAndModelFileRefHandle>(null);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.IModelDetailRes>();

  const initialPanes = useMemo(
    () => [
      {tab: '概览', key: PanelEnum.OVERVIEW},
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(catalog === 'system'
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}])
    ],
    [catalog]
  );

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    return [
      {
        label: '数据模型名称',
        value: info.name
      },
      {
        label: '数据模型ID',
        value: info.id
      },
      {
        label: '数据模型类型',
        value: info.modelType
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '存储类型',
        value: info.storageType
      }
    ];
  }, [dataInfo, userList]);

  // 获取详情
  const getModelDetail = useCallback(async () => {
    const res = await http.getModelDetail(workspaceId, fullName);
    setDataInfo(res.result);
  }, [fullName, workspaceId]);

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getModelDetail();
  }, [catalog, schema, node, getModelDetail]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey: any) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchDatasetOrModel(workspaceId, EnumMetaType.MODELS, fullName, {comment: text});
      getModelDetail();
    },
    [fullName, getModelDetail]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  }, [changeUrlFunReplace]);

  const onDropdownClick = useCallback(
    (key: string) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: 'Model',
          metaType: http.EnumMetaType.MODELS,
          requestFun: http.deleteDatasetOrModel,
          successFun: removeSuccessFun,
          workspaceId: workspaceId
        });
      }
    },
    [fullName, node, removeSuccessFun]
  );

  // 如果 Tab 页在概览页签，则刷新 「版本」列表
  const updataFilelist = debounce(() => {
    fileInfoTableRef?.current?.requestFilelist();
  }, 100);

  // 新建版本弹窗展示
  const [showCreateVersion, setShowCreateVersion] = useState<boolean>(false);

  const [listSize, setListSize] = useState(0);

  const [lastVersion, setLastVersion] = useState(0);

  const renderTab = useMemo(() => {
    const config = {
      [PanelEnum.DETAIL]: <InfoPanel infoList={infoList} title="基本信息" />,
      [PanelEnum.OVERVIEW]: (
        <div>
          <DescriptionEdit
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM}
            authList={dataInfo?.privileges || []}
          />
          <h2 className="title-head">
            版本信息
            <span className="list-size"> 共{listSize}条</span>
          </h2>
          <div>
            <DatasetAndModelFileInfoTable
              ref={fileInfoTableRef}
              userList={userList}
              metaType={http.EnumMetaType.MODELS}
              urlState={urlState}
              changeUrlFun={changeUrlFun}
              dataTotalUpdate={(total: number) => setListSize(total)}
              lastVersionUpdate={(ver: number) => setLastVersion(ver)}
              deleteAuths={[Privilege.CreateModelVersion]}
              verClickAuths={[Privilege.Execute, Privilege.CreateModelVersion]}
            />
          </div>
        </div>
      ),
      [PanelEnum.PERMISSION]: (
        <PermissionManage
          resourceType={ResourceType.Model}
          resourceId={fullName}
          hasInheritedFrom
          name={node}
          onSuccess={getModelDetail}
        />
      )
    };
    return config[tab];
  }, [
    catalog,
    changeUrlFun,
    dataInfo?.comment,
    dataInfo?.privileges,
    fullName,
    infoList,
    listSize,
    node,
    onChangeDescript,
    tab,
    urlState,
    userList,
    getModelDetail
  ]);

  return (
    <div className="work-meta-volume-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-volume" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        createText="创建模型版本"
        createIcon={<OutlinedPlusNew width={16} height={16} />}
        onCreateClick={() => setShowCreateVersion(true)}
        authList={dataInfo?.privileges || []}
        createAuthName={Privilege.CreateModelVersion}
      />
      {/* Tabs */}
      <MetaTabs
        panesList={initialPanes}
        tab={tab}
        onTabChange={onTabChange}
        authList={dataInfo?.privileges || []}
      />
      {renderTab}

      {/** 重命名 Volume 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Model"
        metaType={http.EnumMetaType.MODELS}
        requestFun={http.patchDatasetOrModel}
        successFun={renameSuccessFun}
        limitLength={64}
        forbidIfLimit
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getModelDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
              if (res.success && res.result?.id) {
                return Promise.reject(new Error('该Model名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />
      {/* 新建版本 */}
      <CreateDatasetAndModelVersionModal
        urlState={urlState}
        version={lastVersion}
        currentKey="addModel"
        currentModalInfo={dataInfo}
        isModalVisible={showCreateVersion}
        handleCloseModal={() => {
          setShowCreateVersion(false);
        }}
        createdCallback={() => {
          updataFilelist();
        }}
      ></CreateDatasetAndModelVersionModal>
    </div>
  );
};
export default PanelModel;
