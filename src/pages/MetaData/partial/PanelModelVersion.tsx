import {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {Modal, toast} from 'acud';
import {debounce} from 'lodash';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';

import IconSvg from '@components/IconSvg';
import {useDrop, useRequest} from 'ahooks';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaHasVersionActionBar from '../components/MetaHasVersionActionBar';
import MetaTabs from '../components/MetaTabs';

import DatasetAndModelVersionFileInfoTable, {
  DatasetAndModelVersionFileRefHandle
} from '../components/DatasetAndModelVersionFileInfoTable';

import DatasetAndModelUploadModal from '../components/DatasetAndModelUploadModal';
import {OutlinedButtonUpload, OutlinedDelete} from 'acud-icon';
import {formatBytes} from '@utils/utils';
import {CatalogType} from '@api/metaRequest';

import {Privilege} from '@api/permission/type';
import {WorkspaceContext} from '@pages/index';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2'
}

const initialPanes = [
  {tab: '概览', key: PanelEnum.OVERVIEW},
  {tab: '详情', key: PanelEnum.DETAIL}
];

const PanelModelVersion = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, userList} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW, version = '', path = ''} = urlState;

  const controllerRef = useRef(new AbortController());

  // 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 文件信息 Table 组件的 Ref
  const fileInfoTableRef = useRef<DatasetAndModelVersionFileRefHandle>(null);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.IModelVersionDetailRes>();

  const {workspaceId} = useContext(WorkspaceContext);

  useDrop(document.querySelector('.meta-volume-upload-box .acud-upload-list'), {
    onDrop: (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.dataTransfer) {
        const newEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          clientX: e.clientX,
          clientY: e.clientY,
          screenX: e.screenX,
          screenY: e.screenY,
          dataTransfer: e.dataTransfer
        });
        document.querySelector('.meta-volume-upload-box .drag-content')?.dispatchEvent(newEvent);
      }
    }
  });

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    return [
      {
        label: '版本',
        value: info.name
      },
      {
        label: '版本ID',
        value: info.id
      },
      {
        label: '数据路径',
        value: info.storageLocation
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '基础模型',
        value: info.baseModel
      },
      {
        label: '属性',
        value: JSON.stringify(info.properties) || ''
      }
    ];
  }, [dataInfo, userList]);

  // 获取详情
  const getModelVersionDetail = useCallback(async () => {
    const res = await http.getModelVersionDetail(workspaceId, fullName, version);
    setDataInfo(res.result);
  }, [fullName, version, workspaceId]);

  const [dataType, setDataType] = useState('');
  // 获取数据集当前版本详情
  const getModelDetail = useCallback(async () => {
    const res = await http.getModelDetail(workspaceId, fullName);
    const dataType = res?.result?.dataType || '';
    setDataType(dataType);
  }, [fullName, workspaceId]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey: any) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 如果 Tab 页在概览页签，则刷新 「文件信息」列表
  const updataFilelist = debounce(() => {
    fileInfoTableRef?.current?.requestFilelist(version);
  }, 100);

  // 上传数据弹窗展示
  const [showUploadVersion, setShowUploadVersion] = useState<boolean>(false);

  // 当前版本的文件列表条数
  const [listSize, setListSize] = useState(0);

  // 当前模型的版本总数
  const [versionTotal, setVersionTotal] = useState(50);

  // 当前模型的版本列表
  const [versionList, setVersionList] = useState([]);

  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({
    pageNo: 1,
    pageSize: 10
  });

  // 请求 模型的版本列表
  const {loading, run: getDatasetOrModelVersionList} = useRequest(
    async () => {
      const res = await http.getModelVersionList(workspaceId, fullName, pagination);
      const result: any = res.result || {};
      result.versions = result?.versions?.map((item: any) => ({value: item.name, key: item.id})) || [];
      setVersionList(result.versions);
      result.total = result?.total || 0;
      setVersionTotal(result.total);
    },
    {
      manual: true,
      refreshDeps: [catalog, schema, node, pagination]
    }
  );

  // 下拉加载更多版本
  const loadMoreVersion = useCallback(
    (target: any) => {
      const loadTag =
        target.scrollHeight - target.clientHeight - target.scrollTop < 20 &&
        !loading &&
        versionList.length < versionTotal;
      if (!loadTag) {
        return;
      }
      let currentPage: number = pagination.pageNo;
      setPagination((pre) => ({...pre, pageNo: ++currentPage}));
      getDatasetOrModelVersionList();
    },
    [getDatasetOrModelVersionList, loading, pagination.pageNo, versionList.length, versionTotal]
  );

  // 版本删除方法
  const deleteDatasetOrModelVersion = async (versionName: string) => {
    const res = await http.deleteDatasetOrModelVersion(
      workspaceId,
      http.EnumMetaType.MODELS,
      fullName,
      versionName
    );
    if (res.success) {
      changeUrlFun((pre) => ({...pre, tab: undefined, version: undefined}));
    }
  };

  /**
   * 删除版本图标：事件监听
   */
  const onVersionDelete = () => {
    Modal.confirm({
      title: '确定要删除吗？',
      content: version ? (
        <div>删除后⽆法恢复！请确定是否要删除版本 “{version}”</div>
      ) : (
        <div>删除后⽆法恢复！请确定是否要删除所选择的内容</div>
      ),
      onOk: () =>
        version
          ? deleteDatasetOrModelVersion(version)
          : toast.error({
              message: '版本号缺失，无法进行删除操作',
              duration: 3
            })
    });
  };

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchDatasetOrModelVersionDetail(workspaceId, http.EnumMetaType.MODELS, fullName, version, {
        comment: text
      });
      getModelVersionDetail();
    },
    [fullName, getModelVersionDetail, version, workspaceId]
  );

  const afterCloseUploadFile = () => {
    controllerRef.current.abort();
    controllerRef.current = new AbortController();
  };

  // 如果 Tab 页在概览页签，则刷新 「文件信息」列表
  const onChangeVersion = debounce((version: string) => {
    changeUrlFun((preState: any) => ({...preState, version}));
    fileInfoTableRef?.current?.requestFilelist(version);
  }, 100);

  useEffect(() => {
    return () => {
      controllerRef.current.abort();
    };
  }, []);

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getDatasetOrModelVersionList();
    getModelVersionDetail();
    getModelDetail();
  }, [catalog, schema, node, getDatasetOrModelVersionList, getModelVersionDetail, getModelDetail]);

  return (
    <div className="work-meta-volume-panel">
      {/* 标题导航操作栏 */}
      <MetaHasVersionActionBar
        changeVersionFun={onChangeVersion}
        catalog={catalog}
        icon={<IconSvg type="meta-version" size={20} />}
        currentVersion={version}
        versions={versionList}
        versionsLoading={loading}
        scrollLoadVersion={(target) => {
          loadMoreVersion(target);
        }}
        createText="上传数据到模型"
        createIcon={<OutlinedButtonUpload />}
        onCreateClick={() => setShowUploadVersion(true)}
        deleteIcon={<OutlinedDelete />}
        onDeleteClick={() => {
          onVersionDelete();
        }}
        authList={dataInfo?.privileges || []}
        deleteAuths={[Privilege.CreateModelVersion]}
        uploadAuths={[Privilege.CreateModelVersion]}
      />
      {/* Tabs */}
      <MetaTabs
        panesList={initialPanes}
        tab={tab}
        onTabChange={onTabChange}
        authList={dataInfo?.privileges || []}
      />
      {/* Tab-Panel 1 概览 */}
      {tab === PanelEnum.OVERVIEW ? (
        <div>
          <DescriptionEdit
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM}
            authList={dataInfo?.privileges || []}
          />

          <h2 className="title-head">
            列表信息
            <span className="list-size"> 共{listSize}条</span>
          </h2>
          <div>
            <DatasetAndModelVersionFileInfoTable
              currentKey="model"
              ref={fileInfoTableRef}
              urlState={urlState}
              changeUrlFun={changeUrlFun}
              dataTotalUpdate={(total: number) => setListSize(total)}
              authList={dataInfo?.privileges || []}
              deleteAuths={[Privilege.CreateModelVersion]}
            />
          </div>
        </div>
      ) : null}
      {/* Tab-Panel 2 详情 */}
      {tab === PanelEnum.DETAIL ? <InfoPanel infoList={infoList} title="基本信息" /> : null}
      {/* 上传内容 */}
      <DatasetAndModelUploadModal
        urlState={urlState}
        dataType={dataType}
        currentKey="model"
        metaType={http.EnumMetaType.MODELS}
        isModalVisible={showUploadVersion}
        onCancel={() => {
          setShowUploadVersion(false);
        }}
        onCreateSuccessCallback={updataFilelist}
        defaultDirectory={{catalog, schema, node, path, version}}
        controller={controllerRef.current}
        disabled
        afterCloseUploadFile={afterCloseUploadFile}
      ></DatasetAndModelUploadModal>
    </div>
  );
};
export default PanelModelVersion;
