/*
 * @Author: ho<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2025-07-16 11:50:15
 * @LastEditTime: 2025-07-30 15:15:53
 */
export enum TableSource {
  ICEBERG = 'ICEBERG',
  DORIS = 'DORIS',
}

export enum ETableIceberg {
  MANAGED = 'MANAGED',
  EXTERNAL = 'EXTERNAL',
}

export const IcebergTableTypeMap = {
  [ETableIceberg.MANAGED]: '内部表',
  [ETableIceberg.EXTERNAL]: '外部表'
};

export enum ETableDoris {
  DUPLICATE = 'DUPLICATE_KEY',
  UNIQUE = 'UNIQUE_KEY',
  AGGREGATE = 'AGGREGATE_KEY',
}

export const DorisTableTypeMap = {
  [ETableDoris.DUPLICATE]: '明细模型',
  [ETableDoris.UNIQUE]: '唯一模型',
  [ETableDoris.AGGREGATE]: '聚合模型'
};

export enum ETableDorisBucket {
  HASH = 'HASH',
  RANDOM = 'RANDOM',
}

export const DorisTableBucketMap = {
  [ETableDorisBucket.HASH]: 'Hash分桶',
  [ETableDorisBucket.RANDOM]: 'Random分桶',
};

export const sqlFormatContent = `CREATE TABLE table_name ( \n column1 INT comment 'this is column1',  \n) \nCOMMENT 'table comment'`;
