/*
 * @Author: hong<PERSON><EMAIL>
 * @Date: 2025-07-14 14:17:59
 * @LastEditTime: 2025-08-13 17:03:21
 */
import CreateTableBaseInfo from '../components/CreateTableBaseInfo';
import LeftContentHeader from '../components/LeftContentHeader';
import {Form, Input, Select, Checkbox, InputNumber, Loading} from 'acud';
import styles from './index.module.less';
import {TableSource} from '../const';
import TableEditList from '../components/TableEditList';
import {
  IcebergTableFieldTypeMap,
  IcebergTablePartitionFunc,
  IcebergPartFuncTypes,
  TableHanleModel
} from '@api/meta/table';
import {useCallback, useState, useEffect, useMemo, useContext} from 'react';
import {FormInstance} from 'acud/lib/form';

import * as http from '@api/metaRequest';
import {useRequest} from 'ahooks';
import {WorkspaceContext} from '@pages/index';
import useUrlState from '@ahooksjs/use-url-state';
import {detailDataFormate} from '../utils';
import {RULE} from '@utils/regs';
interface MetaIcebergTableProps {
  form: FormInstance<any>;
  status: string;
  errMsg: string;
  tableHanleWay: string;
  onSilderHandle: () => void;
  onChange: () => void;
}
const MetaIcebergTable: React.FC<MetaIcebergTableProps> = ({
  form,
  status,
  errMsg,
  tableHanleWay,
  onSilderHandle,
  onChange
}) => {
  // 基础信息
  const [{catalog, schema, node}] = useUrlState(undefined, {
    navigateMode: 'replace'
  });

  // 是否是编辑模型
  const editTag = tableHanleWay === TableHanleModel.EDIT;

  // table 详情数据
  const [dataInfo, setDataInfo] = useState<http.ITableDetailRes>();

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // table 全名
  const fullName = `${catalog}.${schema}.${node}`;

  const partitionFields = Form.useWatch('columns', form) || [];

  // 基础内容逻辑
  const selectTypeRenderComponent = (index) => {
    const formValues = form.getFieldsValue();
    const initialType = formValues?.columns?.[index]?.typeName || IcebergTableFieldTypeMap.STRING;

    const [typeScale, setTypeScale] = useState(10);

    const typeMatchField = useMemo(() => {
      switch (initialType) {
        case IcebergTableFieldTypeMap.DECIMAL:
          return (
            <div className={styles['select_render_filed']}>
              <div> ( </div>
              <Form.Item
                name={[index, 'typePrecision']}
                initialValue={typeScale}
                rules={[{required: true, message: '请输入整数类型值', type: 'integer'}]}
              >
                <InputNumber
                  style={{width: 80, display: 'flex'}}
                  onChange={(v) => {
                    onChange();
                    setTypeScale(v);
                  }}
                  max={38}
                  min={1}
                />
              </Form.Item>
              <div>,</div>
              <Form.Item
                name={[index, 'typeScale']}
                initialValue={0}
                rules={[{required: true, message: '请输入整数类型值', type: 'integer'}]}
              >
                <InputNumber
                  style={{width: 80, display: 'flex'}}
                  min={0}
                  max={typeScale}
                  onChange={onChange}
                />
              </Form.Item>
              <div> ) </div>
            </div>
          );
        case IcebergTableFieldTypeMap.FIXED:
          return (
            <div className={styles['select_render_filed']}>
              <div> ( </div>
              <Form.Item
                name={[index, 'typeLength']}
                initialValue={10}
                rules={[{required: true, message: '请输入整数类型值', type: 'integer'}]}
              >
                <InputNumber
                  style={{width: 80, display: 'flex'}}
                  min={1}
                  max={10485760}
                  onChange={onChange}
                />
              </Form.Item>
              <div> ) </div>
            </div>
          );
        case IcebergTableFieldTypeMap.LIST:
          return (
            <div className={styles['select_render_filed']}>
              <Form.Item
                name={[index, 'typeText']}
                rules={[{required: true, message: '请输入参数内容', type: 'string'}]}
              >
                <Input placeholder="<data_type>" onChange={onChange}></Input>
              </Form.Item>
            </div>
          );
        case IcebergTableFieldTypeMap.MAP:
          return (
            <Form.Item
              name={[index, 'typeText']}
              rules={[{required: true, message: '请输入参数内容', type: 'string'}]}
            >
              <Input placeholder="<primitive_type,data_type>" onChange={onChange}></Input>
            </Form.Item>
          );
        case IcebergTableFieldTypeMap.STRUCT:
          return (
            <Form.Item
              name={[index, 'typeText']}
              rules={[{required: true, message: '请输入参数内容', type: 'string'}]}
            >
              <Input placeholder="<col_name:data_type [COMMENT col_comment],...>" onChange={onChange}></Input>
            </Form.Item>
          );
        default:
          return null;
      }
    }, [initialType, typeScale]);

    return (
      <div style={{display: 'flex', alignItems: 'center'}}>
        <Form.Item name={[index, 'typeName']}>
          <Select
            showSearch={true}
            style={{width: 150}}
            options={Object.entries(IcebergTableFieldTypeMap).map(([key, value]) => ({
              label: key,
              value: value
            }))}
            placeholder="请选择类型"
            onChange={(value: any) => {
              onChange();
            }}
          />
        </Form.Item>
        <div className={styles['select_render_container']}>{typeMatchField}</div>
      </div>
    );
  };

  const fieldNameRender = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curColumns = formValues?.columns || [];
      return (
        <Form.Item
          name={[index, 'name']}
          rules={[
            {
              validator: async (_, value: string) => {
                // 校验特殊字符和长度限制
                if (!RULE.specialNameStartEn128.test(value)) {
                  return Promise.reject(new Error(RULE.specialNameStartEn128Text));
                }
                if (
                  value &&
                  curColumns.some(
                    (v, vindex) => index !== vindex && v.name.toLowerCase() === value.toLowerCase()
                  )
                ) {
                  return Promise.reject('字段名称不能重复');
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input placeholder="请输入" onChange={onChange} limitLength={128} />
        </Form.Item>
      );
    },
    [form, onChange]
  );

  const fieldColumnList = [
    {
      title: '字段名称',
      dataIndex: 'ice_name',
      width: 200,
      render: (_, record, index) => fieldNameRender(index)
    },
    {
      title: '字段类型',
      dataIndex: 'ice_typeName',
      render: (_, record, index) => selectTypeRenderComponent(index)
    },
    {
      title: '非空',
      width: 100,
      dataIndex: 'nullable',
      valuePropName: 'checked',
      renderComponent: <Checkbox onChange={onChange}></Checkbox>
    },
    {
      title: '字段描述',
      dataIndex: 'comment',
      width: 200,
      renderComponent: <Input placeholder="请输入" onChange={onChange} limitLength={1024}></Input>
    }
  ];

  // 分区内容逻辑
  const [selectDoneFunc, setSelectDoneFunc] = useState({});
  const selectFuncRenderComponent = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curentPart = useMemo(
        () => formValues?.partitioning?.[index] || {},
        [formValues?.partitioning?.[index]]
      );

      // 选择的转换函数类型
      const [selectFunc, setSelectFunc] = useState('');

      useEffect(() => {
        const initType = curentPart.function || '';
        initType && setSelectFunc(initType);
        if (selectDoneFunc[curentPart.columnName]) {
          setSelectDoneFunc((pre) => {
            pre[curentPart.columnName][index] = initType;
            return {...pre};
          });
        } else {
          setSelectDoneFunc((pre) => {
            pre[curentPart.columnName] = {[index]: initType};
            return {...pre};
          });
        }
      }, [curentPart]);

      const funcMatchField = useMemo(() => {
        if (selectFunc === IcebergPartFuncTypes.bucket || selectFunc === IcebergPartFuncTypes.truncate) {
          return (
            <Form.Item name={[index, 'functionArgument']} initialValue={1}>
              <InputNumber style={{width: 80, display: 'flex'}} min={1} max={100} onChange={onChange} />
            </Form.Item>
          );
        }
        return null;
      }, [selectFunc]);

      const renderSelectOptions = useMemo(() => {
        const curFieldInfo = partitionFields.find((item) => item.name === curentPart.columnName);
        const toUseFunc: any = Object.entries(IcebergTablePartitionFunc).filter(([_, value]) =>
          value.includes(curFieldInfo?.typeName)
        );
        const disabledTag = Object.values(selectDoneFunc[curentPart.columnName] || {}) || [];
        return toUseFunc.map((item: any) => ({
          label: item[0],
          value: item[0],
          disabled: disabledTag.includes(item[0])
        }));
      }, [curentPart, partitionFields, selectDoneFunc]);

      return (
        <div style={{display: 'flex', alignItems: 'center'}}>
          <Form.Item name={[index, 'function']} rules={[{required: true, message: '请选择转换函数'}]}>
            <Select
              style={{width: 150}}
              options={renderSelectOptions}
              placeholder="请选择转换函数"
              onChange={(value: any) => {
                const curentNamePart = selectDoneFunc[curentPart.columnName];
                if (curentNamePart) {
                  setSelectDoneFunc((pre) => {
                    pre[curentPart.columnName][index] = value;
                    return {...pre};
                  });
                } else {
                  setSelectDoneFunc((pre) => {
                    pre[curentPart.columnName] = {[index]: value};
                    return {...pre};
                  });
                }
                setSelectFunc(value);
                onChange();
              }}
            />
          </Form.Item>
          <div className={styles['select_render_container']}>{funcMatchField}</div>
        </div>
      );
    },
    [form, partitionFields, selectDoneFunc]
  );

  const partitionColumnList = [
    {
      title: '字段名称',
      dataIndex: 'columnName',
      rules: [{required: true, message: '请选择名称'}],
      renderComponent: (
        <Select
          style={{width: '100%'}}
          options={partitionFields
            ?.filter((item) => item.name)
            .map((item) => ({
              label: item.name,
              value: item.name,
              disabled:
                item.typeName === IcebergTableFieldTypeMap.STRUCT ||
                item.typeName === IcebergTableFieldTypeMap.MAP
            }))}
          placeholder="请选择字段名称"
          onChange={(v) => {
            onChange();
          }}
        />
      )
    },
    {
      title: '转换函数',
      dataIndex: 'type',
      render: (_, record, index) => selectFuncRenderComponent(index)
    }
  ];

  // 属性名称渲染
  const propsNameRender = useCallback(
    (index) => {
      const formValues = form.getFieldsValue();
      const curProps = formValues?.properties || [];
      return (
        <Form.Item
          name={[index, 'name']}
          rules={[
            {required: true, message: '请输入属性名称'},
            {
              validator: async (_, value: string) => {
                if (
                  value &&
                  curProps.some(
                    (v, vindex) => index !== vindex && v.name.toLowerCase() === value.toLowerCase()
                  )
                ) {
                  return Promise.reject('属性名称不能重复');
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input placeholder="请输入" onChange={onChange} />
        </Form.Item>
      );
    },
    [form, onChange]
  );

  // 表属性内容
  const propsColumnList = [
    {
      title: '属性名称',
      dataIndex: 'name',
      render: (_, record, index) => propsNameRender(index)
    },
    {
      title: '属性值',
      dataIndex: 'value',
      rules: [{required: true, message: '请输入属性值'}],
      renderComponent: <Input placeholder="请输入" onChange={onChange}></Input>
    }
  ];

  const fieldDefaultValue = {
    name: '',
    typeName: IcebergTableFieldTypeMap.STRING,
    nullable: false,
    comment: ''
  };
  const partitionDefaultValue = {columnName: ''};
  const propsDefaultValue = {name: '', value: ''};

  // 获取详情
  const {loading, run: getTableDetail} = useRequest(
    async () => {
      const res = await http.getTableDetail(workspaceId, fullName);
      setDataInfo(res.result);
    },
    {
      manual: true
    }
  );

  useEffect(() => {
    if (editTag) {
      // 拉取详情接口
      getTableDetail();
    }
  }, [editTag]);

  useEffect(() => {
    // 1、详情数据转化
    const formateData = detailDataFormate(dataInfo);
    // 2、表单回显
    form.setFieldsValue(formateData);
  }, [dataInfo]);

  useEffect(() => {
    form.setFieldsValue({columns: [{...fieldDefaultValue}]});
  }, []);

  // 移除分区字段数据
  const removePartionData = useCallback(
    (index) => {
      setSelectDoneFunc((pre) => {
        Object.keys(pre).forEach((key) => {
          if (pre[key][index]) {
            delete pre[key][index];
          }
        });
        const preString = JSON.stringify(pre);
        return {...JSON.parse(preString)};
      });
    },
    [form]
  );

  // 移除字段数据，联动分区信息删除对应分区字段
  const removeField = useCallback(() => {
    const formValues = form.getFieldsValue();
    const curColumns = formValues?.columns || [];
    const curPartition = formValues?.partitioning || [];
    form.setFieldsValue({
      partitioning: curPartition.filter((item) => curColumns.find((citem) => citem.name === item.name))
    });
  }, [form]);

  return (
    <div className={styles['table_content_contailer']}>
      {loading && <Loading></Loading>}

      {!editTag && <LeftContentHeader status={status} errMsg={errMsg} onSilderHandle={onSilderHandle} />}

      <CreateTableBaseInfo
        dataInfo={dataInfo}
        tableHanleWay={tableHanleWay}
        onChange={onChange}
        tableFrom={TableSource.ICEBERG}
      />

      <div className={styles['form-item-table']}>
        <div className={styles['common_title_desc']}>字段信息</div>
        <TableEditList
          showIndex={true}
          initRowData={true}
          isReadOnly={false}
          name="columns"
          form={form}
          columnList={fieldColumnList}
          defaultValue={fieldDefaultValue}
          onRemove={() => {
            onChange();
            removeField();
          }}
        />
      </div>

      <div className={styles['form-item-table']}>
        <div className={styles['common_title_desc']}>分区信息</div>
        <TableEditList
          showIndex={true}
          isReadOnly={false}
          name="partitioning"
          form={form}
          columnList={partitionColumnList}
          defaultValue={partitionDefaultValue}
          addButtonText="添加分区"
          onRemove={(index) => {
            onChange();
            removePartionData(index);
          }}
        />
      </div>

      <div className={styles['form-item-table']}>
        <div className={styles['common_title_desc']}>高级配置</div>
        <TableEditList
          isReadOnly={false}
          name="properties"
          form={form}
          columnList={propsColumnList}
          defaultValue={propsDefaultValue}
          addButtonText="添加表属性"
          onRemove={onChange}
        />
      </div>
    </div>
  );
};

export default MetaIcebergTable;
