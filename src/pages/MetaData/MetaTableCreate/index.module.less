:global {
  div.non-nav-page-content-container {
    display: flex;
  }
}
.meta_table_create_container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  display: flex;
  flex-direction: column;
  background: hsl(0, 0%, 94%);

  .create-content-container {
    flex: 1;
    box-sizing: border-box;
    border-radius: 8px;
    border: 1px solid #E8E9EB;
    background: #fff;
    display: flex;
    flex-direction: column;

    .create-content-title {
      .create_title_container {
        padding: 16px 16px 0;
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .title_content {
          margin-left: 12px;
          font-weight: 500;
          font-style: Medium;
          font-size: 20px;
        }
      }

      .create_title_alert {
        padding: 0 32px;
      }
    }
  }

  .create_content_info {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: 13px;
    border-top: 1px  solid #D4D6D9;

    .title_content {
      color: #151B26;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }

    .form_title {
      color: #151B26;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
  }

  .edit_content_info {
    padding: 16px 32px 0px;
  }

  .create-content-tail {
    padding: 20px;
    display: flex;
    align-items: center;
    border-top: 1px solid #D4D6D9;
  }
}