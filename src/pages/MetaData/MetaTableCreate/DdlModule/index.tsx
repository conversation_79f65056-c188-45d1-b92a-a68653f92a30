/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-07-15 11:40:24
 * @LastEditTime: 2025-07-28 17:25:45
 */
import React, {useCallback, useContext, useMemo, useState, useRef, useEffect} from 'react';
import IconSvg from '@components/IconSvg';
import StatusIcon from '../components/StatusIcon';
import styles from './index.module.less'
import {toast, Drawer, Button} from 'acud';
import SqlEditPage from '@components/SqlEditPage';
import {sqlFormatContent} from "../const"
import {format} from 'sql-formatter';

interface DdlContentProps {
  // ddl sql 内容
  ddlSql?: string;
  // 侧边栏滑动事件
  onSilderHandle: () => void;
  // 修改 ddl sql 内容事件
  onChange: (v: string) => void;
  // ddl sql 转化状态
  status: string;
  // ddl sql 转化的错误信息
  errMsg: string;
}
const DdlContent: React.FC<DdlContentProps> = ({
  onChange,
  onSilderHandle,
  status,
  errMsg = '',
  ddlSql = ''
}) => {
  const [drawVisible, setDrawerVisible] = useState(false);

  const [sqlContent, setSqlContent] = useState(ddlSql);

  const formateDDL = useCallback(() => {
    const newSql = format(sqlContent, {language: 'sql'});
    setSqlContent(newSql);
  }, [sqlContent]);

  const grammarRule = () => {
    setDrawerVisible(true);
  };

  const onClose = () => {
    setDrawerVisible(false);
  };

  useEffect(() => {
    setSqlContent(ddlSql);
  }, [ddlSql])

  return (
    <div id="ddl_modle_container" className={styles['ddl_modle_container']}>
      <div className={styles['ddl_modle_header']}>
        <div className={styles['ddl_modle_header_desc']}>
          <div className={styles['header_desc_title']}>DDL语句</div>
          <StatusIcon status={status} errMsg={errMsg} />
        </div>
        <div className={styles['ddl_modle_header_control']}>
          <div
            className={styles['header_control_item']}
            onClick={() => formateDDL()}
          >
            格式化
          </div>
          <div
            className={styles['header_control_item']}
            onClick={() => grammarRule()}
          >
            语法规范
          </div>
          <div style={{cursor: 'pointer'}}>
            <IconSvg
              type="right"
              size={20}
              color="#5C5F66"
              onClick={onSilderHandle}
            />
          </div>
        </div>
      </div>
      <div className={styles['ddl_modle_content']}>
        <SqlEditPage value={sqlContent} onChange={(v) => {
          onChange(v)
        }} />
      </div>
      <Drawer
        className={styles['drawer_container']}
        title="语法规范"
        placement='bottom'
        height='75%'
        closable
        onClose={onClose}
        visible={drawVisible}
        maskStyle={{backgroundColor: 'rgba(0, 0, 0, 0)'}}
        bodyStyle={{padding: 0, display: 'flex', fontSize: 12, fontWeight: 400}}
        getContainer={() => document.querySelector('#ddl_modle_container')}
        headerStyle={{
          height: 42,
          color: '#151B26', fontSize: 16,
          padding: '8px 0 8px 20px',
          borderTop: '1px solid #EBECF0'
        }}
      >
        <div className={styles['draw_ddl_content']}>
          <SqlEditPage value={sqlFormatContent} isReadOnly />
        </div>
      </Drawer>
    </div >
  );
};

export default DdlContent;